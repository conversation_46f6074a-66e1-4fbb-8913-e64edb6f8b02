# 本项目中的所有常量
import os
import re
from zoneinfo import ZoneInfo

from config import gui_constants

# --------------------- 上下限设置 -----------------------
# WAL文件进行检查的最小大小（MB）
MAX_WAL_SIZE_MB = 100
# SQLite一次处理的消息数量
SQLITE_VARIABLE_LIMIT = 999
# 发信设备标识的最小/最大长度
MIN_CLIENT_KEY_LEN = 10
MAX_CLIENT_KEY_LEN = 30
MIN_CLIENT_DESC_LEN = 1
ERROR_CLIENT_INPUT_MSG = f"当前发信设备信息有误!\n发信设备标识长度必须在{MIN_CLIENT_KEY_LEN}到{MAX_CLIENT_KEY_LEN}之间,且只能包含字母和数字\n发信设备描述长度必须大于{MIN_CLIENT_DESC_LEN}"
# 单条消息的最小/最大长度
MIN_MESSAGE_LEN = 1
MAX_MESSAGE_LEN = 100
# 一次读取的消息上限和下限数量
MIN_UNREAD_MSGS_LEN = 1
MAX_UNREAD_MSGS_LEN = 100
# 数据库中数据所允许存储的数量,超出这个范围的数据中的已读数据会被删除
MAX_MESSAGE_COUNT = 100000
# 获取最新最近minutes分钟内的未读消息上下限[minute]
EARLIEST_RECENTLY_UNREAD_TIME = 1
LATEST_RECENTLY_UNREAD_TIME = 60
# ---------------------------------------------------------------

# 默认时区
DEFAULT_TIMEZONE = ZoneInfo(gui_constants.TIME_ZONE)
UTC_ZONE = ZoneInfo("UTC")
# STORAGE_UNIT中对应的英文单位的最大长度: 目前最长的是 BYTES
STORAGE_UNIT_KEY_MAX_LEN = 5
# 数据存储单位的正则表达式: 1GB/1.1MB/1024KB/1024B
STORAGE_UNIT_PATTERN = re.compile(fr"^([0-9]+(?:\.[0-9]+)?)([A-Za-z]{{0,{STORAGE_UNIT_KEY_MAX_LEN}}})$")
# 对应单位转换成字节的映射关系:不带单位则默认是字节单位
STORAGE_UNIT = {
    '': 1,
    'B': 1,
    'K': 1024,
    'M': 1024 ** 2,
    'G': 1024 ** 3,
    'T': 1024 ** 4,
    'P': 1024 ** 5,
    'E': 1024 ** 6,
    'KB': 1024,
    'MB': 1024 ** 2,
    'GB': 1024 ** 3,
    'TB': 1024 ** 4,
    'PB': 1024 ** 5,
    'EB': 1024 ** 6,
    'KIB': 1024,
    'MIB': 1024 ** 2,
    'GIB': 1024 ** 3,
    'TIB': 1024 ** 4,
    'PIB': 1024 ** 5,
    'EIB': 1024 ** 6,
    'BYTE': 1,
    'BYTES': 1
}



# --------------------- webhook配置相关 ------------------------
LOG_LEVELS = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
# 服务端[server]必须包含的相关配置
SERVER_REQUIRED_KEYS = {
    "api_key", "whitelist","host", "port",
    "message_data_table_name", "log_config_path",
    "run_time", "time_zone", "expire_data_days", "data_limit_num","app_name","enable_sql_logging"
}
# 服务端配置项对应key的中文名称
SERVER_KEY_DESC={
    "api_key":"API KEY", "whitelist":"IP 白名单","host":"web服务器host", "port":"web服务器端口",
    "message_data_table_name":"消息数据存储表名", "log_config_path":"日志配置路径",
    "run_time":"服务器每日运行时段", "time_zone":"时区",
    "expire_data_days":"数据过期保存天数", "data_limit_num":"数据存储上限数量",
    "app_name":"应用名称","enable_sql_logging":"是否启用SQL日志记录",
    "client_info":"发信设备标识信息"
}
# message_data_table_name 表名的要求 (数字字母下划线组合,且只能以字母开头,长度在1-10之间)
MESSAGE_DATA_TABLE_NAME_PATTERN = re.compile(r"^[a-zA-Z][a-zA-Z0-9_]{0,19}$")
# 服务器运行时段的正则表达式:[07:20-19:30]
SERVER_RUN_TIME_PATTERN = re.compile(r"^([01]\d|2[0-3]):([0-5]\d)-([01]\d|2[0-3]):([0-5]\d)$")
# 发信设备标识的正则表达式
CLIENT_KEY_PATTERN = re.compile(fr"^[a-zA-Z0-9]{{{MIN_CLIENT_KEY_LEN},{MAX_CLIENT_KEY_LEN}}}$")
SERVER_API_KEY_MIN_LEN = 10
SERVER_API_KEY_PATTERN = re.compile(fr"^[a-zA-Z0-9]{{{SERVER_API_KEY_MIN_LEN},}}$")
ERROR_SERVER_API_KEY_MSG = f"当前API KEY长度必须大于{SERVER_API_KEY_MIN_LEN}位,且只能包含字母和数字!"
ERROR_WHITELIST_MSG = "当前IP白名单格式有误,必须是IP或者IP段,如: *********** 或 ***********/24,请检查格式是否正确!"
ERROR_MESSAGE_DATA_TABLE_NAME_MSG= "当前消息数据存储表名格式有误,其只能包含字母、数字、下划线,且只能以字母开头,长度在1-20之间,请检查格式是否正确!"
ERROR_LOG_CONFIG_PATH_MSG = "当前日志配置路径不存在或者日志配置文件内容为空,请检查路径是否正确!"
ERROR_HOST_MSG = "当前web服务器host格式有误,其必须是本机局域网IP地址或者域名,请检查格式是否正确!"
ERROR_PORT_MSG = "当前web服务器端口格式有误,其必须是0~65535之间的整数,请检查格式是否正确!"
ERROR_RUNTIME_MSG = "当前服务器每日运行时段格式有误,其必须是HH:MM-HH:MM,请检查格式是否正确!"
ERROR_TIME_ZONE_MSG = "当前时区格式有误,其必须是时区名称,比如:Asia/Shanghai,请检查格式是否正确!"
ERROR_EXPIRE_DATA_DAYS_MSG = "当前无效数据过期保存天数格式有误,其必须是整数,请检查格式是否正确!"
ERROR_DATA_LIMIT_NUM_MSG = "当前数据存储上限数量格式有误,其必须是整数,请检查格式是否正确!"
ERROR_APP_NAME_MSG = "当前应用名称格式有误,其不能为空!"
ERROR_SERVER_CONFIG_MISS_SERVER_MSG="当前服务端配置文件中不存在[server]配置节点,请检查配置文件内容是否正确!"
ERROR_SERVER_CONFIG_MISS_CLIENT_INFO_MSG="当前服务端配置文件中不存在[client_info]配置节点,请检查配置文件内容是否正确!"
ERROR_ENABLE_SQL_LOGGING_MSG="当前是否启用SQL日志记录格式有误,其必须是布尔值,请检查格式是否正确!"
MIN_PORT = 0
MAX_PORT = 65535

# 所有的`webhook-server`服务器的api接口
API_LIST = {"/webhook/token","/webhook/save","/webhook/unread"}
# 需要校验ip的api接口
IP_CHECK_API_LIST = {"/webhook/token","/webhook/unread"}
# 不同的webhook_server实例中关键配置项绝对不能重复 --- 其中 host-port 组合必须唯一 log配置全局唯一所有实例的log配置都会是一致的 后续唯一性配置转移到config_unique_constraints中，该常量不使用 todo
DIFF_INSTANCE_UNIQUE_KEYS = {"message_data_table_name","app_name"}

# 定时任务调度器的默认配置
DEFAULT_SCHEDULER_CONFIG={'misfire_grace_time': 15, 'coalesce': True, 'max_instances': 2}
# 多进程数据共享的数据库路径【多进程下各个进程的配置数据唯一,历史配置文件路径】 --- cross_process_config_lock
CROSS_PROCESS_DATA_BASE_PATH=os.path.join(os.path.expanduser("~"), ".webhook_server", "config_data.db")

# 在logger没有生效的情况下print输出的临时日志文件路径
TEMP_LOG_FILE_PATH=os.path.join(os.path.expanduser("~"), ".webhook_server", "temp_log_")

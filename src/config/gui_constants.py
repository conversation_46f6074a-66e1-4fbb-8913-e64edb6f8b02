# gui界面中涉及到的常量值
import os
import re

# 软件基础信息
VERSION = "1.0.1"
SOFTWARE_NAME = "多类别设备数据接收存储服务端"
CONTACT_INFORMATION = "<EMAIL>"
RELEASE_TIME = "2025-06-01"
INSTRUCTION_SOFTWARE_USE = "该软件可以用于接收并存储多种类型设备的实时数据并分发给指定的第三方使用。"
LICENSE_AGREEMENT = "本软件遵循Apache License 2.0开源协议发布,用户应遵守相关法律法规和协议使用条款。"

# 默认值
DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"
# 软件图标路径
ICON_PATH = "D:\\Git\\python-samples-hub\\resources\\xzc.ico"
# 配置项默认值
WHITELIST = "*"
SERVER_CONFIG_DIR=os.path.join(os.path.expanduser("~"), ".webhook_server")
APP_NAME_PREFIX = "服务端配置_"
MESSAGE_DATA_TABLE_NAME_PREFIX="message_data_"
# 服务器配置文件名正则匹配:新建的gui界面的配置文件名称的正则表达式
SERVER_CONFIG_FILENAME_PREFIX="server_config_"
SERVER_CONFIG_FILENAME_SUFFIX=".ini"
SERVER_CONFIG_FILENAME_PATTERN = re.compile(rf'{re.escape(SERVER_CONFIG_FILENAME_PREFIX)}(\d+){re.escape(SERVER_CONFIG_FILENAME_SUFFIX)}')
LOG_CONFIG_PATH = os.path.join(os.path.expanduser("~"), ".webhook_server", "log_config.ini")
SERVER_CONFIG_PATH = os.path.join(os.path.expanduser("~"), ".webhook_server", "server_config.ini")
HOST = "0.0.0.0"
PORT = 8000
RUN_TIME = "07:00-07:00"
TIME_ZONE = "Asia/Shanghai"
EXPIRE_DATA_DAYS = 3
DATA_LIMIT_NUM = 100000
# 用户可视化界面中可自定义的服务端配置项,在加载配置时不需要进行校验
USER_CUSTOM_KEYS = {"api_key"}
# 在使用gui界面时,服务器配置项值需要在多进程之间唯一,在新建/加载配置时需要进行校验
GUI_SERVER_CONFIG_UNIQUE_KEYS = {"port","message_data_table_name","app_name"}
DEFAULT_GUI_WIDTH = 500
DEFAULT_GUI_HEIGHT = 300
ENABLE_SQL_LOGGING = True

# 以下是log相关的配置 DEBUG
LOG_LEVEL = "ERROR"
LOG_CONSOLE = True
LOG_FILE = True
LOG_DIR = os.path.join(os.path.expanduser("~"), ".webhook_server", "logs")
LOG_FILE_NAME_PREFIX = "gui_webhook_server"
LOG_FILE_DATE_FMT = "%Y-%m-%d"
LOG_FILENAME = "%(filename_prefix)s-%(levelname)s-%(filename_date_fmt)s.log"
LOG_FILE_MAX_SIZE = "10MB"
LOG_BACKUP_COUNT = 3
LOG_FORMAT = "%(asctime)s [%(levelname)s] [PID:%(process)d] %(message)s"
LOG_DATE_FMT = DATETIME_FORMAT
# 日志保留天数[其删除也只会删除指定filename_prefix前缀的日志文件]
LOG_EXPIRE_LOGS_DAYS = 5
LOG_ZONE = TIME_ZONE
# 日志颜色配置
LOG_COLOR_DEBUG="LIGHTBLACK_EX"
LOG_COLOR_INFO="LIGHTWHITE_EX"
LOG_COLOR_WARNING="YELLOW"
LOG_COLOR_ERROR="RED"
LOG_COLOR_CRITICAL="BRIGHT+WHITE"

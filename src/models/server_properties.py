# 当前服务端配置属性值 --- 加载logger配置 --- 初始化 server_data_manager
import configparser
import logging
import os.path
from datetime import datetime
from typing import Dict, Any, Optional
from zoneinfo import ZoneInfo

from apscheduler.schedulers.asyncio import AsyncIOScheduler

from config import constants
from models import server_data_manager
from utils import self_log, server_utils


class ServerProperties:
    def __init__(self, server_properties_path: str, custom_scheduler_config: dict=None):
        self.logger: Optional[logging.Logger] =  logging.getLogger(__name__) if self_log.log_config.had_init() else None # noqa
        server_utils.logger_print(msg=f"initializing server properties with path: {server_properties_path}",custom_logger=self.logger)
        properties_path = server_utils.get_real_exist_file_path(server_properties_path)
        server_utils.logger_print(msg=f"this complete cultural properties path: {properties_path}",custom_logger=self.logger)

        self.server_properties_path = properties_path
        self.scheduler_default_config = custom_scheduler_config if custom_scheduler_config is not None else constants.DEFAULT_SCHEDULER_CONFIG
        server_utils.logger_print(msg=f"scheduler config set: {self.scheduler_default_config}",custom_logger=self.logger)
        self.scheduler:Optional[AsyncIOScheduler] = None

        server_utils.logger_print(msg="parsing properties",custom_logger=self.logger)
        self._parse_properties()
        server_utils.logger_print(msg="properties parsed successfully",custom_logger=self.logger)

        server_utils.logger_print(msg="initializing other components",custom_logger=self.logger)
        self._init_other()
        server_utils.logger_print(msg="other components initialized",custom_logger=self.logger)

        self.start_datetime = datetime.now(self.server_config_zone)
        server_utils.logger_print(msg=f"server properties initialization completed at: {self.start_datetime}",custom_logger=self.logger)

    def _parse_properties(self):
        config = configparser.ConfigParser(interpolation=None)
        server_utils.logger_print(msg=f"reading config file: {self.server_properties_path}", custom_logger=self.logger)
        config.read(self.server_properties_path, encoding="utf-8")
        server_utils.logger_print(msg="config file read successfully", custom_logger=self.logger)

        # 检查配置文件[server]中配置项是否合法,然后转换类型
        server_utils.logger_print(msg="parsing server config section", custom_logger=self.logger)
        server_properties = ServerProperties.get_server_config(config)

        server_utils.logger_print(msg="parsing client info section", custom_logger=self.logger)
        client_info_properties = ServerProperties.get_client_info(config)

        # 该字段有效性已经进行检查
        self.server_config_zone = ZoneInfo(server_properties["time_zone"])
        server_utils.logger_print(msg=f"timezone set successfully: {self.server_config_zone}", custom_logger=self.logger)

        self.server_config = server_properties
        self.client_info_properties = client_info_properties
        server_utils.logger_print(msg="properties parsing completed", custom_logger=self.logger)


    @staticmethod
    def get_client_info(config: configparser.ConfigParser) -> Dict[str, Any]:
        client_info_properties = server_utils.section_to_dict(config, "client_info")
        server_utils.check_all_client_info_config(client_info_properties)
        return client_info_properties

    # 检查配置文件[server]中配置项是否合法,然后转换类型
    @staticmethod
    def get_server_config(config: configparser.ConfigParser) -> Dict[str, Any]:
        server_config = server_utils.section_to_dict(config, "server")
        ServerProperties.check_reset_server_config(server_config)
        return server_config

    @staticmethod
    def _check_server_config_app_name(server_config: dict):
        cur_key = "app_name"
        app_name = server_utils.trim(server_config.get(cur_key))
        if app_name is None:
            raise ValueError(f"server properties [server]-[app_name] value must not be empty!")
        server_config[cur_key] = app_name
    @staticmethod
    def _check_server_config_api_key(server_config: dict):
        cur_key = "api_key"
        api_key = server_config.get(cur_key)
        if not server_utils.check_server_api_key(api_key):
            # api_key需要有一定的复杂度[当前复杂度简单检测:长度大于10且由字母和数字组成]
            raise ValueError(constants.ERROR_SERVER_API_KEY_MSG)

    @staticmethod
    def _check_server_config_whitelist(server_config: dict):
        cur_key = "whitelist"
        whitelist = server_config.get(cur_key)
        server_config[cur_key] = server_utils.get_whitelist(whitelist)

    @staticmethod
    def _check_server_config_message_data_table_name(server_config: dict):
        cur_key = "message_data_table_name"
        message_data_table_name = server_utils.trim(server_config.get(cur_key))
        if message_data_table_name is None:
            raise ValueError(f"server properties [server]-[message_data_table_name] value must not be empty!")
        # 数字字母下划线组合,且只能以字母开头,长度在1-20之间
        if not constants.MESSAGE_DATA_TABLE_NAME_PATTERN.match(message_data_table_name):
            raise ValueError(f"server properties [server]-[message_data_table_name] value must be must be a combination of numbers, letters, underscores, and can only start with a letter, with a length between 1-20,current value is {message_data_table_name}")
        server_config[cur_key] = message_data_table_name

    @staticmethod
    def _check_server_config_log_config_path(server_config: dict):
        cur_key = "log_config_path"
        log_config_path = server_config.get(cur_key)
        log_config_path_str = server_utils.get_real_exist_file_path(log_config_path)
        if not os.path.isfile(log_config_path_str) or os.path.getsize(log_config_path_str) <= 0:
            raise ValueError(
                f"server properties [server]-[log_config_path] value:{log_config_path_str} must exist and not empty!")
        server_config[cur_key] = log_config_path_str

    @staticmethod
    def _check_server_config_host(server_config: dict):
        cur_key = "host"
        host = server_config.get(cur_key)
        server_utils.check_lan_host(host)

    @staticmethod
    def _check_server_config_port(server_config: dict):
        cur_key = "port"
        port = server_config.get(cur_key)
        server_config[cur_key] = server_utils.get_server_config_port(port)

    @staticmethod
    def _check_server_config_run_time(server_config: dict):
        cur_key = "run_time"
        run_time = server_config.get(cur_key)
        if not constants.SERVER_RUN_TIME_PATTERN.match(run_time):
            raise ValueError(
                f"server properties [server]-[run_time] value must be in format of HH:MM-HH:MM,current value is {run_time}")

    @staticmethod
    def _check_server_config_time_zone(server_config: dict):
        cur_key = "time_zone"
        # 该值未改变类型,所以需要进行后续校验操作
        time_zone = server_config.get(cur_key)
        server_utils.check_time_zone_str(time_zone)

    @staticmethod
    def _check_server_config_expire_data_days(server_config: dict):
        cur_key = "expire_data_days"
        expire_data_days = server_config.get(cur_key)
        server_config[cur_key] = server_utils.tran_int(cur_key, expire_data_days)

    @staticmethod
    def _check_server_config_data_limit_num(server_config: dict):
        cur_key = "data_limit_num"
        data_limit_num = server_config.get(cur_key)
        server_config[cur_key] = server_utils.tran_int(cur_key, data_limit_num)

    @staticmethod
    def check_reset_server_config(server_config: dict):
        missing = constants.SERVER_REQUIRED_KEYS - server_config.keys()
        if missing:
            raise ValueError(f"server properties [server] missing required keys: {missing}")
        # 由于之前key和value都已经trim操作了,所以这里方法中对应配置项不需要再次trim
        # 其中字段如果已经不是字符串类型,执行了一次该函数进行类型转换,就不需要进行字符串类型相关操作
        # 校验配置项是否合法
        ServerProperties._check_server_config_app_name(server_config)
        ServerProperties._check_server_config_api_key(server_config)
        ServerProperties._check_server_config_whitelist(server_config)
        ServerProperties._check_server_config_message_data_table_name(server_config)
        ServerProperties._check_server_config_log_config_path(server_config)
        ServerProperties._check_server_config_host(server_config)
        ServerProperties._check_server_config_port(server_config)
        ServerProperties._check_server_config_run_time(server_config)
        ServerProperties._check_server_config_time_zone(server_config)
        ServerProperties._check_server_config_expire_data_days(server_config)
        ServerProperties._check_server_config_data_limit_num(server_config)


    # 外部最后必须调用
    def clear(self):
        server_utils.run_once(self._clear)

    def _clear(self):
        server_utils.logger_print(msg="server properties clear ...", custom_logger=self.logger)
        try:
            if hasattr(self, "scheduler") and self.scheduler is not None and self.scheduler.running:
                try:
                    # 由于logging模块会在atexit时关闭,导致其不能使用,所以就关闭了调度器中的logger使用
                    self.scheduler._logger.setLevel(100) # noqa
                    self.scheduler.shutdown(wait=True)
                except BaseException as e:
                    server_utils.logger_print(msg=f"error shutting down scheduler!", custom_logger=self.logger, use_exception=True, exception=e, print_error=True)
                finally:
                    self.scheduler = None
        finally:
            if hasattr(self, "data_manager") and self.data_manager is not None:
                self.data_manager = None
            self.start_datetime = None
            self.client_info_properties.clear()
            self.server_config.clear()
            self.server_config_zone = None
            self.server_properties_path = None


    # 初始化其他操作[如日志配置等]
    def _init_other(self):
        # 初始化定时任务调度器
        self.scheduler = AsyncIOScheduler(job_defaults=self.scheduler_default_config, timezone=self.server_config_zone)
        server_utils.logger_print(msg=f"scheduler created !", custom_logger=self.logger)

        # self.scheduler.start()
        server_utils.logger_print(msg="scheduler started successfully", custom_logger=self.logger)

        # shutdown_exec.register(self.clear) #其他地方会执行该操作
        self_log.setup_logging(self.server_config["log_config_path"],time_zone= self.server_config_zone,filename_prefix=self.server_config["app_name"])
        if self.logger is None:
            self.logger = logging.getLogger(__name__)
        server_utils.logger_print(msg="logging setup completed", custom_logger=self.logger)
        message_data_table_name=self.server_config['message_data_table_name']
        enable_sql_logging=self.server_config['enable_sql_logging']
        self.data_manager = server_data_manager.WebhookDataManager(db_path=constants.CROSS_PROCESS_DATA_BASE_PATH,table_name=message_data_table_name, zone=self.server_config_zone, enable_sql_logging=enable_sql_logging)
        server_utils.logger_print(msg="data manager initialized successfully", custom_logger=self.logger)


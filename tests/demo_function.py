import asyncio
import configparser
import logging
import os
import re
import sys
import threading
import time
from datetime import datetime, timedelta
from ipaddress import ip_network, ip_address
from zoneinfo import ZoneInfo

import uvicorn
from fastapi import FastAPI

from models import webhook_server

src_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src'))
if src_path not in sys.path:
    sys.path.append(src_path)
from utils import self_log, shutdown_exec_funct as shutdown_exec, server_utils
import config.constants as constants

app = FastAPI()

self_log.setup_logging("../resources/log.ini")
logger = logging.getLogger(__name__)

cn_zone = ZoneInfo("Asia/Shanghai")
shutdown_flag = asyncio.Event()


class MyClass:
    def __init__(self, value: int):
        self.value = value

    # 类实例函数
    def instance_method(self) -> str:
        """操作实例属性"""
        return str(self.value * 2)

    # 类静态函数
    @staticmethod
    def static_method(data):
        """数据处理静态方法"""
        return sum(data) / len(data)


def once_func_1():
    print("函数执行！")


def once_func_2() -> int:
    return 1


def thread_worker():
    server_utils.run_once(once_func_1)
    print(f"test_func_2:{server_utils.run_once(once_func_2)}")
    print(f"test_func_2:{server_utils.run_once(once_func_2)}")
    print(f"instance_method_result:{server_utils.run_once(MyClass(10).instance_method)}")
    print(f"static_method:{server_utils.run_once(MyClass.static_method, [1, 2, 3])}")


def run_once_test():
    threads = []
    for _ in range(10):
        t = threading.Thread(target=thread_worker)
        threads.append(t)
        t.start()

    for t in threads:
        t.join()

# 创建空白内容文件并设置最后修改时间
def create_file_with_mtime(directory, filename, mtime):
    # 构造完整文件路径
    file_path = os.path.join(directory, filename)
    # 创建空文件（如果已存在则会被清空）
    with open(file_path, 'w') as f:
        pass  # 可以在这里写入内容

    # 处理时间格式
    if isinstance(mtime, (int, float)):
        timestamp = mtime
    else:
        # 假设传入的是 datetime 对象
        timestamp = time.mktime(mtime.timetuple())

    # 设置文件的时间戳（访问时间和修改时间）
    os.utime(file_path, (timestamp, timestamp))
    print(f"文件 '{file_path}' 已创建，并设置最后修改时间为 {time.ctime(timestamp)}")


def ip_or_network_test():
    ip = ip_network("127.0.0.1", strict=False)
    print(ip)
    ip = ip_network("10.0.0.0/8", strict=False)
    print(ip)
    ip = ip_network("2001:0db8:85a3:0000:0000:8a2e:0370:7334/64", strict=False)
    print(ip)
    ip = ip_network("fc00::/7", strict=False)
    print(ip)
    ip_address("127.0.0.1")
    ip_address("0.0.0.0")


def date_test():
    cur_cn_time = datetime.now(cn_zone)
    print(cur_cn_time)


def create_expire_log_files():
    desired_log_dir = "../logs"
    desired_log_prefix = "webhook"
    desired_date_time = datetime(2024, 1, 1, 0, 0, 0, tzinfo=cn_zone)
    filename = desired_log_prefix + "-info-xxx.log"
    create_file_with_mtime(desired_log_dir, filename, desired_date_time)


async def loop_log():
    start_time = time.time()
    duration = 1
    while time.time() - start_time < duration:
        logger.debug("This is a debug message")
        logger.info("This is an info message")
        logger.warning("This is a warning message")
        logger.error("This is an error message")
        logger.critical("This is a critical message")


async def async_log_test():
    config = uvicorn.Config(
        app,
        log_config=None,
        log_level=None,
        access_log=False
    )
    server = uvicorn.Server(config)
    # 异步启动
    server_task = asyncio.create_task(server.serve())
    loop_log_task = asyncio.create_task(loop_log())
    await asyncio.gather(server_task, loop_log_task, return_exceptions=True)


# 校验配置文件参数项-重新赋值:[需要根据后续配置项修改进行更新代码校验]
def check_reset_server_config(config: dict):
    missing = constants.SERVER_REQUIRED_KEYS - set(config.keys())
    if missing:
        raise ValueError(f"Missing required keys: {missing}")
    if not re.match(r"\d{2}:\d{2}-\d{2}:\d{2}", config["run_time"]):
        raise ValueError("Invalid run_time format")
    api_key = config["api_key"]
    # api_key需要有一定的复杂度[当前复杂度简单检测:长度大于10]
    if not api_key or not api_key.strip() or len(api_key.strip()) < 11:
        raise ValueError("api_key can not be empty and must have at least 10 characters!")
    message_data_table_name = config["message_data_table_name"]
    log_config_path = config["log_config_path"]
    whitelist = config["whitelist"]

    if not message_data_table_name or not message_data_table_name.strip():
        raise ValueError("message_data_table_name can not be empty!")
    if not log_config_path or not log_config_path.strip():
        raise ValueError("log_config_path can not be empty!")
    if not whitelist or not whitelist.strip():
        raise ValueError("whitelist can not empty!")

    log_config_path = log_config_path.strip()
    if not os.path.isfile(log_config_path) or os.path.getsize(log_config_path) <= 0:
        raise ValueError("log_config_path must exist and not empty!")

    whitelist = whitelist.split(",")
    whitelist = [whitelist_ip.strip() for whitelist_ip in whitelist]
    res_list = []
    for whitelist_ip in whitelist:
        # 校验字符串必须是IP或者网段
        whitelist_ip = whitelist_ip.strip()
        if not whitelist_ip:
            continue
        # 校验IP或者网段是否合法
        ip_network(whitelist_ip, strict=False)
        res_list.append(whitelist_ip)
    if not res_list:
        raise ValueError("whitelist can not empty!")
    # 必须是局域网IP
    host_ip = ip_address(config["host"])
    if not host_ip.is_private:
        raise ValueError("host must be a private IP address!")

    # 重新赋值[后续如果新增或者修改配置项,这里也需要更新]
    config["api_key"] = api_key.strip()
    config["whitelist"] = res_list
    config["message_data_table_name"] = message_data_table_name.strip()
    config["log_config_path"] = os.path.abspath(log_config_path)
    config["host"] = config["host"].strip()
    config["port"] = int(config["port"])
    config["run_time"] = config["run_time"].strip()


def read_ini_list():
    config_path = "../resources/server_config.ini"

    # 读取配置文件
    config = configparser.ConfigParser(interpolation=None)
    config.read(config_path, encoding="utf-8")
    server_config = config["server"]
    server_config = dict(server_config)
    check_reset_server_config(server_config)
    logger.warning(server_config)


def end_do_1():
    logger.info("do_1 end")


def end_do_2():
    logger.info("do_2 end")


def end_do_3(info: str):
    logger.info(f"do_3 end, info: {info}")


def end_do_4(info: str):
    logger.info(f"do_4 end, info: {info}")


end_do_value = 1


def end_do_5():
    logger.info(f"do_5 end, value: {end_do_value}")


def end_do_run_ok():
    start_time = time.time()
    loop_seconds = 10
    while True:
        time.sleep(1)
        logger.info("run ok...")
        if time.time() - start_time > loop_seconds:
            break
    logger.info("end_do_run_ok end")


def end_do_run_error():
    start_time = time.time()
    loop_seconds = 10
    while True:
        time.sleep(1)
        logger.info("run error...")
        if time.time() - start_time > loop_seconds:
            raise ValueError("end_do_run_error raise an exception")


def end_do_always_run():
    logger.info("end_do_always_run start")
    while True:
        time.sleep(1)
        logger.info("end_do_always_run doing something...")


async def end_do_async_run():
    start_time = time.time()
    loop_seconds = 10
    while True:
        time.sleep(1)
        logger.info("async run ok...")
        if time.time() - start_time > loop_seconds:
            break
    logger.info("end_do_async_run end")


async def end_do_async_run_error():
    start_time = time.time()
    loop_seconds = 10
    while True:
        time.sleep(1)
        logger.info("async run error...")
        if time.time() - start_time > loop_seconds:
            raise ValueError("end_do_async_run_error raise an exception")

def end_do_unregister():
    time.sleep(1)
    logger.info("end_do_unregister do")

def end_do_unregister_self():
    time.sleep(1)
    logger.info("end_do_unregister-2 do")
    # 内部进行取消注册 --- 无效
    shutdown_exec.unregister_function(func=end_do_unregister_self)
def end_do():
    register_id=shutdown_exec.register(end_do_unregister)
    shutdown_exec.unregister(register_id)
    shutdown_exec.register(end_do_unregister)
    shutdown_exec.unregister_function(func=end_do_unregister)
    shutdown_exec.register(end_do_unregister_self)
    shutdown_exec.register(end_do_1)
    # 重复注册应该无效
    shutdown_exec.register(end_do_1)
    shutdown_exec.register(end_do_1)
    # shutdown_exec.register(end_do_2)
    # shutdown_exec.register(end_do_3, wait=True,info="xxx")
    # shutdown_exec.register(end_do_4, wait=True,info="yyy")
    # shutdown_exec.register(end_do_5)
    # end_do_run_ok()
    # end_do_run_error()
    # end_do_always_run()
    # asyncio.run(end_do_async_run())
    # asyncio.run(end_do_async_run_error())
    logger.info("main")
    time.sleep(3)


async def async_job():
    logger.info("async_job info")
    shutdown_flag.set()


# 测试调度器是否可以先启动后新增定时任务
def background_schedule_start_add_job():
    from apscheduler.schedulers.background import BackgroundScheduler
    scheduler = BackgroundScheduler(timezone=constants.DEFAULT_TIMEZONE)
    scheduler.start()
    shutdown_exec.register(scheduler.shutdown)
    scheduler.add_job(func=end_do_1, trigger="interval", seconds=3, id="do_1")
    scheduler.add_job(func=end_do_2, trigger="interval", seconds=5, id="do_2")
    # 不适合直接添加异步任务
    # scheduler.add_job(func=async_job,  trigger="interval", seconds=1, id="async_job")
    while True:
        time.sleep(10)
        logger.info("schedule_start_add_job doing something...")


# 涉及到事件循环:
async def asyncio_schedule_start_add_job():
    from apscheduler.schedulers.asyncio import AsyncIOScheduler
    scheduler = AsyncIOScheduler()
    scheduler.configure(timezone=constants.DEFAULT_TIMEZONE)
    logger.info(f"scheduler timezone: {scheduler.timezone}")
    scheduler.start()
    shutdown_exec.register(scheduler.shutdown)
    scheduler.add_job(func=end_do_1, trigger="interval", seconds=3, id="do_1")
    scheduler.add_job(func=end_do_2, trigger="interval", seconds=5, id="do_2")
    run_date = datetime.now(cn_zone) + timedelta(seconds=10)
    scheduler.add_job(func=async_job, trigger="date", run_date=run_date, id="async_job")
    await shutdown_flag.wait()


def datetime_test():
    logger.info(datetime.now(cn_zone))
    logger.info(datetime.strptime("07:00", "%H:%M"))
    now = datetime.now(cn_zone)
    now_time = now.time().replace(second=0, microsecond=0)
    logger.info(f"current datetime: {now}, time: {now_time}")


def str_test():
    level = "INFO"
    level = level.lower() if level else ""
    logger.info(level)
    level = "info"
    level = level.lower() if level else ""
    logger.info(level)
    level = ""
    level = level.lower() if level else ""
    logger.info(level)

def run_many_server():
    config_path = "D:/Git/python-samples-hub/resources/server_config.ini"
    asyncio.run(webhook_server.run_server_with_path(config_path))
    print("Server started")
    asyncio.run(webhook_server.run_server_with_path(config_path))

def run_many_server_test():
    run_many_server()
    time.sleep(10)

def dict_equals_test():
    d1 = {"a": 1, "b": 2}
    d2 = {"b": 2, "a": 1}
    print(d1 == d2)
    d3 = {"a": 1, "b": 2, "c": 3}
    d4 = {"b": 2, "a": 1}
    print(d3 == d4)
    d5 = {"a": 1, "b": 2, "c": 3}
    d6 = {"b": 2, "a": 1, "c": 3}
    print(d5 == d6)
    d7 = {"a": 1, "b": 2, "c": 3}
    d8 = {"b": 2, "a": 1, "c": 4}
    print(d7 == d8)
    d9 = {"a": 1, "b": 2, "c": {"d": 4}}
    d10 = {"b": 2, "a": 1, "c": {"d": 4}}
    print(d9 == d10)

def is_local_host_test():
    error_ip_list=["asd","asd","locl","local","localhos","300.000.00.11"]
    for ip in error_ip_list:
        try:
            print(f"{ip} --- {server_utils.is_local_host(ip)}")
        except ValueError as e:
            logger.error(f"is_local_host({ip}) error: {e}")
    no_local_host=["*******","***********"]
    for ip in no_local_host:
        print(f"{ip} --- {server_utils.is_local_host(ip)}")
    local_host=["127.0.0.1","localhost","************"]
    for ip in local_host:
        print(f"{ip} --- {server_utils.is_local_host(ip)}")

def format_time_interval_test():
    past_time = time.monotonic() -  timedelta(days=1).total_seconds()
    logger.info(f"format_time_interval(past_time): {server_utils.format_time_monotonic_interval(past_time)}")
    past_time = time.monotonic() - timedelta(hours=1).total_seconds()
    logger.info(f"format_time_interval(past_time): {server_utils.format_time_monotonic_interval(past_time)}")
    past_time = time.monotonic() - timedelta(minutes=1).total_seconds()
    logger.info(f"format_time_interval(past_time): {server_utils.format_time_monotonic_interval(past_time)}")
    past_time = time.monotonic()- timedelta(seconds=1).total_seconds()
    logger.info(f"format_time_interval(past_time): {server_utils.format_time_monotonic_interval(past_time)}")

def is_port_in_use_test():
    local_ip = server_utils.get_local_lan_ip()
    print(f"port 8080 is in use: {server_utils.is_port_occupied(8000)}")
    print(f"local ip: {local_ip}, port 8000 is in use: {server_utils.is_port_occupied(8000, ip=local_ip)}")

if __name__ == '__main__':
    # ip_or_network_test()
    # date_test()
    # asyncio.run(async_log_test())
    # asyncio.run(loop_log())
    # create_expire_log_files()
    # read_ini_list()
    # end_do()
    # background_schedule_start_add_job()
    # asyncio.run(asyncio_schedule_start_add_job())
    # logger.info(ServerProperties._tran_int("1"))
    # logger.info(ServerProperties._tran_int("1234567890123456789012345678901234567890"))
    # logger.info(ServerProperties._tran_int("a"))
    # str_test()
    # run_once_test()
    # run_many_server_test()
    # dict_equals_test()
    # is_local_host_test()
    # format_time_interval_test()
    # print(f"cur machine ip: {server_utils.get_local_lan_ip()}")
    is_port_in_use_test()

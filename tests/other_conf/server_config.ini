#服务端配置参数
[server]
# 第三方 获取未读信息的 API 密钥
api_key=vZMnVhJzW3Y5ejt6Yoyhm
# 所允许的第三方获取未读信息的 IP 白名单 [如果存在多个值，用逗号分隔]
whitelist=127.0.0.1,
message_data_table_name=test_messages
log_config_path=D:\Git\python-samples-hub\tests\other_conf\log.ini
host=***********
port=8000
run_time=07:00-23:50
time_zone=Asia/Shanghai
# 已读数据过期时间（天） 非正数表示永不过期
expire_data_days=5
# 最大数据条数限制:超限则删除超限部分的已读数据 非正数表示不限制
data_limit_num=100000

# 发信方标识及其说明
[client_info]
xascxadsas5c:测试设备1
kxnjhJhGFcgf:测试设备2
